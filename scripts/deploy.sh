#!/bin/bash

# 🚀 AI简历顾问自动化部署脚本
# 使用微信开发者工具CLI自动部署云函数

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_PATH="/Users/<USER>/WeChatProjects/miniprogram-3"
CLI_PATH="/Applications/wechatwebdevtools.app/Contents/MacOS/cli"
APPID="wx851e169cec69d8d2"

echo -e "${BLUE}🚀 开始部署AI简历顾问云函数...${NC}"

# 检查CLI工具
if [ ! -f "$CLI_PATH" ]; then
    echo -e "${RED}❌ 微信开发者工具CLI未找到，请先安装微信开发者工具${NC}"
    exit 1
fi

# 检查项目路径
if [ ! -d "$PROJECT_PATH" ]; then
    echo -e "${RED}❌ 项目路径不存在: $PROJECT_PATH${NC}"
    exit 1
fi

echo -e "${YELLOW}📂 项目路径: $PROJECT_PATH${NC}"
echo -e "${YELLOW}🔧 CLI路径: $CLI_PATH${NC}"

# 切换到项目目录
cd "$PROJECT_PATH"

# 检查登录状态
echo -e "${BLUE}🔐 检查登录状态...${NC}"
if ! "$CLI_PATH" islogin --project "$PROJECT_PATH"; then
    echo -e "${YELLOW}⚠️  需要登录微信开发者工具${NC}"
    echo -e "${BLUE}🔑 正在打开登录界面...${NC}"
    "$CLI_PATH" login --project "$PROJECT_PATH"
    
    # 等待用户登录
    echo -e "${YELLOW}请在微信开发者工具中完成登录，然后按回车继续...${NC}"
    read -r
fi

echo -e "${GREEN}✅ 登录状态正常${NC}"

# 部署aiChat云函数
echo -e "${BLUE}📦 部署aiChat云函数...${NC}"
if "$CLI_PATH" cloud functions:deploy aiChat --project "$PROJECT_PATH"; then
    echo -e "${GREEN}✅ aiChat云函数部署成功${NC}"
else
    echo -e "${RED}❌ aiChat云函数部署失败${NC}"
    exit 1
fi

# 部署conversationManage云函数
echo -e "${BLUE}📦 部署conversationManage云函数...${NC}"
if "$CLI_PATH" cloud functions:deploy conversationManage --project "$PROJECT_PATH"; then
    echo -e "${GREEN}✅ conversationManage云函数部署成功${NC}"
else
    echo -e "${RED}❌ conversationManage云函数部署失败${NC}"
    exit 1
fi

# 检查云函数状态
echo -e "${BLUE}🔍 检查云函数状态...${NC}"
"$CLI_PATH" cloud functions:list --project "$PROJECT_PATH"

echo -e "${GREEN}🎉 所有云函数部署完成！${NC}"
echo -e "${BLUE}💡 现在可以在小程序中测试AI功能了${NC}"
echo -e "${YELLOW}📱 建议访问 pages/ai-test/ai-test 页面进行测试${NC}"

# 可选：自动打开预览
read -p "是否要打开小程序预览？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}📱 正在生成预览...${NC}"
    "$CLI_PATH" preview --project "$PROJECT_PATH" --desc "AI功能部署完成"
fi

echo -e "${GREEN}🎊 部署完成！${NC}"
