#!/bin/bash

# 🚀 AI简历顾问快速部署脚本

echo "🚀 开始部署AI简历顾问云函数..."

# 项目配置
PROJECT_PATH="/Users/<USER>/WeChatProjects/miniprogram-3"
CLI_PATH="/Applications/wechatwebdevtools.app/Contents/MacOS/cli"
ENV_ID="aicv-3g1rr6glfc42a1eb"

# 切换到项目目录
cd "$PROJECT_PATH"

echo "📦 部署aiChat云函数..."
"$CLI_PATH" cloud functions deploy --env "$ENV_ID" --names aiChat --project "$PROJECT_PATH" --remote-npm-install

echo "📦 部署conversationManage云函数..."
"$CLI_PATH" cloud functions deploy --env "$ENV_ID" --names conversationManage --project "$PROJECT_PATH" --remote-npm-install

echo "🎉 部署完成！现在可以测试AI功能了"
echo "💡 建议访问小程序的 pages/ai-test/ai-test 页面进行测试"
