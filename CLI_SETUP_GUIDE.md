# 🛠️ 微信开发者工具CLI配置指南

## ✅ CLI工具已安装

微信开发者工具CLI已经配置完成！

### 📍 CLI路径
```bash
/Applications/wechatwebdevtools.app/Contents/MacOS/cli
```

## 🚀 快速部署命令

### 方法一：使用npm脚本（推荐）

```bash
# 部署所有云函数
npm run deploy

# 单独部署aiChat云函数
npm run deploy:aiChat

# 单独部署conversationManage云函数
npm run deploy:conversation

# 查看云函数状态
npm run status

# 生成预览二维码
npm run preview

# 打开项目
npm run open
```

### 方法二：直接使用CLI命令

```bash
# 部署aiChat云函数
/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy \
  --env aicv-3g1rr6glfc42a1eb \
  --names aiChat \
  --project /Users/<USER>/WeChatProjects/miniprogram-3 \
  --remote-npm-install

# 部署conversationManage云函数
/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy \
  --env aicv-3g1rr6glfc42a1eb \
  --names conversationManage \
  --project /Users/<USER>/WeChatProjects/miniprogram-3 \
  --remote-npm-install
```

### 方法三：使用脚本

```bash
# 执行自动部署脚本
./deploy-ai.sh
```

## ⚙️ 首次使用配置

### 1. 启用IDE服务端口

1. **打开微信开发者工具**
2. **进入设置** → **安全设置**
3. **开启服务端口**
4. **确认端口号**（通常是6060）

### 2. 登录验证

```bash
# 检查登录状态
/Applications/wechatwebdevtools.app/Contents/MacOS/cli islogin \
  --project /Users/<USER>/WeChatProjects/miniprogram-3

# 如果未登录，执行登录
/Applications/wechatwebdevtools.app/Contents/MacOS/cli login \
  --project /Users/<USER>/WeChatProjects/miniprogram-3
```

## 🔧 常用命令

### 云函数管理

```bash
# 查看所有云函数
npm run status

# 部署指定云函数
/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy \
  --env aicv-3g1rr6glfc42a1eb \
  --names 函数名 \
  --project /Users/<USER>/WeChatProjects/miniprogram-3

# 下载云函数
/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions download \
  --env aicv-3g1rr6glfc42a1eb \
  --names 函数名 \
  --project /Users/<USER>/WeChatProjects/miniprogram-3
```

### 项目管理

```bash
# 打开项目
npm run open

# 生成预览
npm run preview

# 构建npm
/Applications/wechatwebdevtools.app/Contents/MacOS/cli build-npm \
  --project /Users/<USER>/WeChatProjects/miniprogram-3

# 上传代码
/Applications/wechatwebdevtools.app/Contents/MacOS/cli upload \
  --project /Users/<USER>/WeChatProjects/miniprogram-3 \
  --desc "版本描述"
```

## 🐛 故障排除

### 问题1：IDE service port disabled
**解决方案**：
1. 打开微信开发者工具
2. 设置 → 安全设置 → 开启服务端口
3. 重新执行命令

### 问题2：登录失效
**解决方案**：
```bash
npm run login
```

### 问题3：环境ID错误
**解决方案**：
检查 `miniprogram/app.js` 中的环境ID是否与命令中的一致

### 问题4：权限不足
**解决方案**：
确认微信账号有该小程序的开发权限

## 🎯 自动化工作流

### 开发流程

1. **修改代码**
2. **本地测试**
3. **部署云函数**：`npm run deploy`
4. **生成预览**：`npm run preview`
5. **测试功能**

### 持续集成

可以将这些命令集成到CI/CD流程中：

```yaml
# GitHub Actions示例
- name: Deploy Cloud Functions
  run: |
    npm run deploy:aiChat
    npm run deploy:conversation
```

## 🎉 部署成功验证

部署完成后，可以通过以下方式验证：

1. **查看云函数状态**：`npm run status`
2. **在小程序中测试**：访问 `pages/ai-test/ai-test`
3. **查看云开发控制台**：确认函数已部署

---

现在您可以使用这些命令快速部署和管理AI简历顾问的云函数了！🚀
