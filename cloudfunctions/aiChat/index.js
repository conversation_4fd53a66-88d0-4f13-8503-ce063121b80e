// cloudfunctions/aiChat/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * AI简历顾问云函数
 * 使用腾讯云开发AI能力，打造专业的简历制作助手
 *
 * 设计理念：
 * - 简洁：一个函数处理所有AI交互
 * - 直观：清晰的对话流程和状态管理
 * - 强大：智能信息提取和个性化建议
 */
exports.main = async (event, context) => {
  const { message, conversationId, currentStage, resumeData, history = [] } = event
  const { OPENID } = cloud.getWXContext()

  try {
    // 构建专业的简历顾问系统提示词
    const systemPrompt = buildResumeAdvisorPrompt(currentStage, resumeData)

    // 构建对话消息
    const messages = buildConversationMessages(systemPrompt, history, message, currentStage)

    // 调用腾讯云AI生成回复
    const aiResponse = await generateAIResponse(messages)

    // 智能提取简历信息
    const extractedData = await extractResumeInfo(message, aiResponse, currentStage)

    // 确定下一个对话阶段
    const nextStage = determineNextStage(currentStage, extractedData)

    // 生成智能建议
    const suggestions = generateSmartSuggestions(currentStage, nextStage, extractedData)

    // 更新对话记录
    await updateConversationRecord(conversationId, OPENID, message, aiResponse, extractedData)

    return {
      success: true,
      aiResponse: aiResponse,
      extractedData: extractedData,
      nextStage: nextStage,
      suggestions: suggestions,
      conversationId: conversationId
    }
  } catch (error) {
    console.error('AI对话处理失败:', error)
    return {
      success: false,
      error: error.message,
      // 优雅降级
      aiResponse: getGracefulFallback(currentStage),
      extractedData: null,
      nextStage: currentStage,
      suggestions: []
    }
  }
}

/**
 * 构建专业的简历顾问系统提示词
 * 体现乔布斯的产品理念：专注、简洁、用户至上
 */
function buildResumeAdvisorPrompt(stage, resumeData) {
  const basePrompt = `你是小简，一位温暖专业的简历顾问，具有以下特质：

🎯 核心使命：帮助用户发现自己的价值，制作出色的简历

💫 对话风格：
- 温暖友善，像朋友一样交流
- 专业敏锐，善于发现用户的亮点
- 耐心引导，深入挖掘用户经历
- 积极鼓励，让用户重新认识自己的价值

📋 当前阶段：${getStageDescription(stage)}

💡 已收集信息：${formatResumeData(resumeData)}

🎨 回复要求：
- 使用"你"而不是"您"，保持亲切感
- 适当使用emoji增加亲和力
- 避免机械化和模板化表达
- 每次回复控制在100字以内，简洁有力
- 根据用户回答给出具体的追问或建议`

  return basePrompt
}

/**
 * 构建对话消息数组
 */
function buildConversationMessages(systemPrompt, history, currentMessage, stage) {
  const messages = [
    { role: 'system', content: systemPrompt }
  ]

  // 添加历史对话（最近5轮）
  const recentHistory = history.slice(-10) // 保留最近10条消息
  messages.push(...recentHistory)

  // 添加当前用户消息
  messages.push({
    role: 'user',
    content: `[当前阶段: ${stage}] ${currentMessage}`
  })

  return messages
}

/**
 * 调用腾讯云AI生成回复
 */
async function generateAIResponse(messages) {
  try {
    // 暂时使用智能模拟回复，确保功能稳定
    return generateSmartMockResponse(messages)

    // TODO: 后续集成真实AI
    /*
    // 使用腾讯云开发AI能力
    const aiModel = cloud.extend.AI.createModel('deepseek')

    const response = await aiModel.generateText({
      model: 'deepseek-v3',
      messages: messages,
      max_tokens: 500,
      temperature: 0.8,
      top_p: 0.9
    })

    // 检查响应格式
    if (response && response.choices && response.choices[0] && response.choices[0].message) {
      return response.choices[0].message.content
    } else {
      console.error('AI响应格式异常:', response)
      throw new Error('AI响应格式异常')
    }
    */
  } catch (error) {
    console.error('AI调用失败:', error)
    // 返回降级回复
    return getFallbackResponse()
  }
}

/**
 * 生成智能模拟回复
 */
function generateSmartMockResponse(messages) {
  // 获取最后一条用户消息
  const lastUserMessage = messages.filter(msg => msg.role === 'user').pop()
  const userContent = lastUserMessage ? lastUserMessage.content.toLowerCase() : ''

  // 智能回复逻辑
  if (userContent.includes('你好') || userContent.includes('hi') || userContent.includes('hello')) {
    return '你好！我是小简，你的专属简历顾问 😊 我会帮你挖掘职业亮点，制作出色的简历。先告诉我你想找什么样的工作吧？'
  }

  if (userContent.includes('工程师') || userContent.includes('开发') || userContent.includes('程序员')) {
    return '技术岗位很不错！💻 能具体说说你想做哪个方向的开发吗？比如前端、后端、移动端等。另外，你目前的技术背景是怎样的？'
  }

  if (userContent.includes('产品') || userContent.includes('经理')) {
    return '产品经理是个很有挑战性的职位！📊 你对哪个行业的产品比较感兴趣？有相关的项目经验吗？'
  }

  if (userContent.includes('我叫') || userContent.includes('我是')) {
    return '很高兴认识你！👋 现在我们来聊聊你的教育背景吧，你是从哪所学校毕业的？学的什么专业？'
  }

  if (userContent.includes('大学') || userContent.includes('学院') || userContent.includes('毕业')) {
    return '你的教育背景很不错！🎓 接下来说说你的工作经验吧，有过实习或正式工作经历吗？'
  }

  if (userContent.includes('公司') || userContent.includes('工作') || userContent.includes('经验')) {
    return '你的工作经历很有价值！💼 最后告诉我你掌握了哪些技能？比如编程语言、工具软件、语言能力等。'
  }

  if (userContent.includes('技能') || userContent.includes('会') || userContent.includes('掌握')) {
    return '太棒了！🎉 现在我已经收集了足够的信息。你的简历信息很丰富，我来帮你整理一下，制作一份专业的简历！'
  }

  // 默认回复
  return '谢谢你的分享！能再详细说说吗？这样我能更好地帮你完善简历。💡'
}

/**
 * 获取降级回复
 */
function getFallbackResponse() {
  const fallbacks = [
    '很高兴认识你！我是小简，你的专属简历顾问。先告诉我你想找什么样的工作吧？',
    '好的，我了解你的想法了。能告诉我更多关于你的背景信息吗？',
    '谢谢你的分享！让我们继续完善你的简历信息。',
    '你的经历很有价值！还有什么想补充的吗？'
  ]

  return fallbacks[Math.floor(Math.random() * fallbacks.length)]
}

/**
 * 智能提取简历信息
 * 使用AI辅助结构化信息提取
 */
async function extractResumeInfo(message, aiResponse, stage) {
  try {
    // 暂时使用规则提取，避免AI调用问题
    return fallbackExtraction(message, stage)

    // TODO: 后续优化AI提取
    /*
    // 构建信息提取提示词
    const extractPrompt = buildExtractionPrompt(message, stage)

    // 使用AI进行结构化提取
    const aiModel = cloud.extend.AI.createModel('deepseek')
    const extractResponse = await aiModel.generateText({
      model: 'deepseek-v3',
      messages: [
        { role: 'system', content: extractPrompt },
        { role: 'user', content: message }
      ],
      max_tokens: 300,
      temperature: 0.1 // 降低温度确保准确性
    })

    // 解析AI返回的结构化数据
    if (extractResponse && extractResponse.choices && extractResponse.choices[0]) {
      return parseExtractedData(extractResponse.choices[0].message.content, stage)
    }
    */
  } catch (error) {
    console.error('信息提取失败:', error)
  }

  // 降级到规则提取
  return fallbackExtraction(message, stage)
}

/**
 * 构建信息提取提示词
 */
function buildExtractionPrompt(message, stage) {
  const prompts = {
    intention: `请从用户消息中提取求职意向信息，以JSON格式返回：
{
  "position": "职位名称",
  "industry": "行业",
  "location": "期望工作地点",
  "salary": "期望薪资"
}
如果某项信息未提及，请设为null。`,

    basicInfo: `请从用户消息中提取个人基本信息，以JSON格式返回：
{
  "name": "姓名",
  "phone": "手机号",
  "email": "邮箱",
  "location": "所在地",
  "age": "年龄"
}
如果某项信息未提及，请设为null。`,

    education: `请从用户消息中提取教育背景信息，以JSON格式返回：
{
  "school": "学校名称",
  "major": "专业",
  "degree": "学历",
  "graduationYear": "毕业年份",
  "gpa": "成绩"
}
如果某项信息未提及，请设为null。`,

    experience: `请从用户消息中提取工作经验信息，以JSON格式返回：
{
  "company": "公司名称",
  "position": "职位",
  "duration": "工作时长",
  "responsibilities": "主要职责",
  "achievements": "主要成就"
}
如果某项信息未提及，请设为null。`,

    skills: `请从用户消息中提取技能信息，以JSON格式返回：
{
  "technical": ["技术技能1", "技术技能2"],
  "soft": ["软技能1", "软技能2"],
  "languages": ["语言1", "语言2"],
  "certifications": ["证书1", "证书2"]
}
如果某项信息未提及，请设为空数组。`
  }

  return prompts[stage] || prompts.intention
}

/**
 * 解析AI提取的结构化数据
 */
function parseExtractedData(aiResponse, stage) {
  try {
    // 尝试解析JSON
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0])
    }
  } catch (error) {
    console.error('JSON解析失败:', error)
  }

  // 降级到规则提取
  return fallbackExtraction(aiResponse, stage)
}

/**
 * 降级信息提取
 */
function fallbackExtraction(message, stage) {
  const extractedData = {}

  switch (stage) {
    case 'intention':
      if (message.includes('工程师') || message.includes('开发')) {
        extractedData.jobIntention = { position: message.trim() }
      }
      break
    case 'basicInfo':
      const nameMatch = message.match(/我叫(.+)|我是(.+)|姓名[：:](.+)/)
      if (nameMatch) {
        extractedData.personalInfo = {
          name: (nameMatch[1] || nameMatch[2] || nameMatch[3]).trim()
        }
      }
      break
    case 'education':
      if (message.includes('大学') || message.includes('学院')) {
        extractedData.education = [{
          school: '用户提到的学校',
          major: '相关专业'
        }]
      }
      break
    case 'experience':
      if (message.includes('公司') || message.includes('工作')) {
        extractedData.workExperience = [{
          company: '用户提到的公司',
          position: '职位'
        }]
      }
      break
    case 'skills':
      const skills = []
      const skillKeywords = ['JavaScript', 'Python', 'Java', 'React', 'Vue', 'Node.js']
      skillKeywords.forEach(skill => {
        if (message.includes(skill)) {
          skills.push(skill)
        }
      })
      if (skills.length > 0) {
        extractedData.skills = skills
      }
      break
  }

  return extractedData
}

/**
 * 确定下一个对话阶段
 */
function determineNextStage(currentStage, extractedData) {
  const stageFlow = {
    greeting: 'intention',
    intention: 'basicInfo',
    basicInfo: 'education',
    education: 'experience',
    experience: 'skills',
    skills: 'completion',
    completion: 'completion'
  }
  
  return stageFlow[currentStage] || 'completion'
}

/**
 * 生成智能建议
 * 根据当前阶段和已收集信息生成个性化建议
 */
function generateSmartSuggestions(currentStage, nextStage, extractedData) {
  const suggestions = {
    greeting: ['前端开发工程师', '产品经理', '数据分析师', 'UI设计师'],
    intention: ['我叫张三', '我在北京工作', '我的手机是138****8888'],
    basicInfo: ['本科毕业', '研究生学历', '专科学历', '海外留学'],
    education: ['刚毕业没经验', '有1-3年经验', '有3年以上经验', '实习经验丰富'],
    experience: ['JavaScript开发', 'Python数据分析', '项目管理', 'Excel高级应用'],
    skills: ['预览简历', '继续完善信息', '选择简历模板', '导出PDF'],
    completion: ['下载简历', '分享简历', '继续优化', '查看其他模板']
  }

  return suggestions[nextStage] || suggestions[currentStage] || []
}

/**
 * 获取阶段描述
 */
function getStageDescription(stage) {
  const descriptions = {
    greeting: '初次见面，了解用户需求',
    intention: '收集求职意向信息',
    basicInfo: '收集个人基本信息',
    education: '了解教育背景',
    experience: '挖掘工作经验',
    skills: '整理技能特长',
    completion: '完成信息收集'
  }
  return descriptions[stage] || '对话进行中'
}

/**
 * 格式化简历数据
 */
function formatResumeData(resumeData) {
  if (!resumeData) return '暂无信息'

  const parts = []
  if (resumeData.personalInfo?.name) {
    parts.push(`姓名：${resumeData.personalInfo.name}`)
  }
  if (resumeData.jobIntention?.position) {
    parts.push(`求职意向：${resumeData.jobIntention.position}`)
  }
  if (resumeData.education?.length > 0) {
    parts.push(`教育背景：${resumeData.education[0].school}`)
  }
  if (resumeData.workExperience?.length > 0) {
    parts.push(`工作经验：${resumeData.workExperience[0].company}`)
  }

  return parts.length > 0 ? parts.join('，') : '暂无信息'
}

/**
 * 更新对话记录
 */
async function updateConversationRecord(conversationId, userId, message, aiResponse, extractedData) {
  try {
    const db = cloud.database()
    const record = {
      conversationId,
      userId,
      userMessage: message,
      aiResponse,
      extractedData,
      timestamp: new Date()
    }

    await db.collection('chatRecords').add({ data: record })
  } catch (error) {
    console.error('更新对话记录失败:', error)
  }
}

/**
 * 优雅降级回复
 */
function getGracefulFallback(stage) {
  const fallbacks = {
    greeting: '很高兴认识你！我是小简，你的专属简历顾问。先告诉我你想找什么样的工作吧？',
    intention: '好的，我了解你的求职意向了。现在告诉我你的基本信息吧，比如姓名、联系方式等。',
    basicInfo: '谢谢你的基本信息！接下来聊聊你的教育背景吧。',
    education: '你的教育背景很不错！现在说说你的工作经验吧。',
    experience: '你的工作经历很有价值！最后告诉我你掌握了哪些技能？',
    skills: '太棒了！现在我已经收集了足够的信息来为你制作简历。',
    completion: '🎉 恭喜！你的简历信息已经收集完成。'
  }

  return fallbacks[stage] || '抱歉，我遇到了一些问题，请稍后再试。'
}

/**
 * 更新对话记录
 */
async function updateConversationRecord(conversationId, userId, userMessage, aiResponse, extractedData) {
  const db = cloud.database()
  
  // 这里应该更新对话记录，暂时省略具体实现
  console.log('更新对话记录:', { conversationId, userId, userMessage, aiResponse, extractedData })
}

/**
 * 获取降级回复
 */
function getFallbackResponse(stage) {
  return '抱歉，我现在有点忙，请稍后再试。或者你可以继续告诉我更多信息。'
}
