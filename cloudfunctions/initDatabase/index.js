// cloudfunctions/initDatabase/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 数据库初始化云函数
 * 创建必要的集合和索引
 */
exports.main = async (event, context) => {
  const { action } = event

  try {
    switch (action) {
      case 'createCollections':
        return await createCollections()
      case 'createIndexes':
        return await createIndexes()
      case 'initData':
        return await initSampleData()
      default:
        return await fullInit()
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 创建集合
 */
async function createCollections() {
  const collections = [
    'userProfiles',    // 用户简历信息
    'chatRecords',     // 聊天记录
    'conversations',   // 对话会话
    'resumeTemplates', // 简历模板
    'systemSettings'   // 系统设置
  ]

  const results = []

  for (const collectionName of collections) {
    try {
      await db.createCollection(collectionName)
      results.push({
        collection: collectionName,
        status: 'created'
      })
    } catch (error) {
      if (error.message.includes('already exists')) {
        results.push({
          collection: collectionName,
          status: 'exists'
        })
      } else {
        results.push({
          collection: collectionName,
          status: 'error',
          error: error.message
        })
      }
    }
  }

  return {
    success: true,
    results: results
  }
}

/**
 * 创建索引
 */
async function createIndexes() {
  const indexes = [
    {
      collection: 'userProfiles',
      indexes: [
        { keys: { _openid: 1 }, unique: true },
        { keys: { updatedAt: -1 } },
        { keys: { 'completeness.overall': -1 } }
      ]
    },
    {
      collection: 'chatRecords',
      indexes: [
        { keys: { userId: 1, timestamp: -1 } },
        { keys: { conversationId: 1 } }
      ]
    },
    {
      collection: 'conversations',
      indexes: [
        { keys: { userId: 1, createdAt: -1 } },
        { keys: { status: 1 } }
      ]
    }
  ]

  const results = []

  for (const indexConfig of indexes) {
    try {
      for (const index of indexConfig.indexes) {
        await db.collection(indexConfig.collection).createIndex({
          keys: index.keys,
          unique: index.unique || false
        })
      }
      results.push({
        collection: indexConfig.collection,
        status: 'indexes_created'
      })
    } catch (error) {
      results.push({
        collection: indexConfig.collection,
        status: 'error',
        error: error.message
      })
    }
  }

  return {
    success: true,
    results: results
  }
}

/**
 * 初始化示例数据
 */
async function initSampleData() {
  const results = []

  // 创建简历模板
  try {
    const templates = [
      {
        name: '经典模板',
        type: 'classic',
        description: '简洁大方的经典简历模板',
        preview: '/images/template-classic.png',
        config: {
          colors: {
            primary: '#333333',
            secondary: '#666666',
            accent: '#007AFF'
          },
          fonts: {
            title: 'PingFang SC',
            body: 'PingFang SC'
          },
          layout: 'single-column'
        },
        isDefault: true,
        createdAt: new Date()
      },
      {
        name: '现代模板',
        type: 'modern',
        description: '时尚现代的简历模板',
        preview: '/images/template-modern.png',
        config: {
          colors: {
            primary: '#2C3E50',
            secondary: '#7F8C8D',
            accent: '#3498DB'
          },
          fonts: {
            title: 'PingFang SC',
            body: 'PingFang SC'
          },
          layout: 'two-column'
        },
        isDefault: false,
        createdAt: new Date()
      }
    ]

    for (const template of templates) {
      await db.collection('resumeTemplates').add({
        data: template
      })
    }

    results.push({
      type: 'resumeTemplates',
      status: 'created',
      count: templates.length
    })
  } catch (error) {
    results.push({
      type: 'resumeTemplates',
      status: 'error',
      error: error.message
    })
  }

  // 创建系统设置
  try {
    const settings = {
      appName: 'AI简历顾问',
      version: '1.0.0',
      features: {
        aiChat: true,
        profileCards: true,
        resumeGeneration: true,
        templateSelection: true
      },
      limits: {
        maxEducationRecords: 10,
        maxWorkExperience: 20,
        maxProjects: 15,
        maxSkills: 50
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await db.collection('systemSettings').add({
      data: settings
    })

    results.push({
      type: 'systemSettings',
      status: 'created'
    })
  } catch (error) {
    results.push({
      type: 'systemSettings',
      status: 'error',
      error: error.message
    })
  }

  return {
    success: true,
    results: results
  }
}

/**
 * 完整初始化
 */
async function fullInit() {
  const results = {
    collections: await createCollections(),
    indexes: await createIndexes(),
    sampleData: await initSampleData()
  }

  return {
    success: true,
    message: '数据库初始化完成',
    details: results
  }
}
