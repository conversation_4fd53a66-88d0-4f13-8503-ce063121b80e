// cloudfunctions/userProfile/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 用户信息管理云函数
 * 负责用户简历数据的增删改查
 */
exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'get':
        return await getUserProfile(OPENID)
      case 'update':
        return await updateUserProfile(OPENID, data)
      case 'create':
        return await createUserProfile(OPENID, data)
      case 'delete':
        return await deleteUserProfile(OPENID)
      default:
        throw new Error('无效的操作类型')
    }
  } catch (error) {
    console.error('用户信息操作失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取用户信息
 */
async function getUserProfile(openid) {
  const result = await db.collection('userProfiles').where({
    _openid: openid
  }).get()

  if (result.data.length > 0) {
    return {
      success: true,
      data: result.data[0]
    }
  } else {
    // 创建默认用户信息
    const defaultProfile = createDefaultProfile()
    const createResult = await db.collection('userProfiles').add({
      data: {
        ...defaultProfile,
        _openid: openid,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })

    return {
      success: true,
      data: {
        _id: createResult._id,
        ...defaultProfile
      }
    }
  }
}

/**
 * 更新用户信息
 */
async function updateUserProfile(openid, updateData) {
  const result = await db.collection('userProfiles').where({
    _openid: openid
  }).update({
    data: {
      ...updateData,
      updatedAt: new Date()
    }
  })

  return {
    success: true,
    updated: result.stats.updated
  }
}

/**
 * 创建用户信息
 */
async function createUserProfile(openid, profileData) {
  const result = await db.collection('userProfiles').add({
    data: {
      ...profileData,
      _openid: openid,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  return {
    success: true,
    _id: result._id
  }
}

/**
 * 删除用户信息
 */
async function deleteUserProfile(openid) {
  const result = await db.collection('userProfiles').where({
    _openid: openid
  }).remove()

  return {
    success: true,
    deleted: result.stats.removed
  }
}

/**
 * 创建默认用户信息结构
 */
function createDefaultProfile() {
  return {
    // 基本信息
    personalInfo: {
      name: '',
      phone: '',
      email: '',
      location: '',
      avatar: '',
      birthday: '',
      gender: ''
    },
    
    // 求职意向
    jobIntention: {
      position: '',
      industry: '',
      location: '',
      salaryRange: '',
      jobType: 'fulltime' // fulltime, parttime, internship
    },
    
    // 教育背景
    education: [],
    
    // 工作经验
    workExperience: [],
    
    // 项目经验
    projects: [],
    
    // 技能特长
    skills: {
      technical: [],
      soft: [],
      languages: [],
      certifications: []
    },
    
    // 自我评价
    selfEvaluation: '',
    
    // 完成度统计
    completeness: {
      personalInfo: 0,
      jobIntention: 0,
      education: 0,
      workExperience: 0,
      projects: 0,
      skills: 0,
      selfEvaluation: 0,
      overall: 0
    }
  }
}
