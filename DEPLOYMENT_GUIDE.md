# 🚀 AI简历顾问部署指南

## 📋 部署前检查

### 1. 确认环境配置
确保 `miniprogram/app.js` 中的云开发环境ID正确：
```javascript
wx.cloud.init({
  env: "aicv-3g1rr6glfc42a1eb", // 您的环境ID
  traceUser: true,
});
```

### 2. 检查云函数文件
确认以下文件存在：
- `cloudfunctions/aiChat/index.js` ✅
- `cloudfunctions/aiChat/package.json` ✅
- `cloudfunctions/conversationManage/index.js` ✅
- `cloudfunctions/conversationManage/package.json` ✅

## 🔧 部署步骤

### 方法一：微信开发者工具部署（推荐）

1. **打开微信开发者工具**
2. **确保已登录并选择正确的项目**
3. **部署 aiChat 云函数**：
   - 右键点击 `cloudfunctions/aiChat` 文件夹
   - 选择"上传并部署：云端安装依赖"
   - 等待部署完成（约1-3分钟）

4. **部署 conversationManage 云函数**：
   - 右键点击 `cloudfunctions/conversationManage` 文件夹
   - 选择"上传并部署：云端安装依赖"
   - 等待部署完成

### 方法二：命令行部署

```bash
# 安装云开发CLI工具
npm install -g @cloudbase/cli

# 登录
tcb login

# 部署云函数
tcb functions:deploy aiChat
tcb functions:deploy conversationManage
```

## ✅ 验证部署

### 1. 在开发者工具中验证
- 点击"云开发" → "云函数"
- 确认看到 `aiChat` 和 `conversationManage` 函数
- 状态显示为"部署成功"

### 2. 功能测试
- 在小程序中访问 `pages/ai-test/ai-test` 页面
- 发送测试消息："你好"
- 应该收到AI回复

## 🐛 常见问题

### 问题1：FunctionName parameter could not be found
**原因**：云函数未部署或部署失败
**解决**：
1. 检查网络连接
2. 重新部署云函数
3. 确认环境ID正确

### 问题2：权限不足
**原因**：没有云开发环境的管理权限
**解决**：
1. 确认微信账号有该小程序的开发权限
2. 联系小程序管理员添加权限

### 问题3：依赖安装失败
**原因**：网络问题或依赖冲突
**解决**：
1. 检查网络连接
2. 清除 `node_modules` 重新安装
3. 使用"云端安装依赖"选项

## 🎯 部署后配置

### 1. 数据库集合创建
在云开发控制台创建以下集合：
- `conversations` - 存储对话记录
- `chatRecords` - 存储聊天记录
- `resumeData` - 存储简历数据

### 2. 权限配置
设置数据库权限：
```json
{
  "read": "auth != null",
  "write": "auth != null"
}
```

## 🚀 性能优化

### 1. 云函数优化
- 设置合理的内存配置（建议512MB）
- 配置环境变量
- 启用日志监控

### 2. 前端优化
- 添加请求缓存
- 实现离线降级
- 优化用户体验

## 📊 监控和维护

### 1. 日志监控
- 在云开发控制台查看云函数日志
- 监控调用次数和错误率
- 设置告警规则

### 2. 性能监控
- 监控响应时间
- 查看资源使用情况
- 优化热点代码

## 🎉 部署完成

部署成功后，您的AI简历顾问就可以正常工作了！

### 测试建议
1. 测试基本对话功能
2. 验证信息提取准确性
3. 检查建议生成效果
4. 测试错误处理机制

### 下一步
- 优化AI提示词
- 添加更多功能
- 收集用户反馈
- 持续改进体验

---

如有问题，请参考：
- [腾讯云开发文档](https://docs.cloudbase.net/)
- [微信小程序云开发指南](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- 项目中的 `AI_INTEGRATION_README.md`
