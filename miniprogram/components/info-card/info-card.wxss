/* components/info-card/info-card.wxss */

/* 卡片容器 */
.info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.info-card.completed::before {
  opacity: 1;
}

.info-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  align-items: center;
}

.icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.progress-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: conic-gradient(#667eea 0deg, #667eea calc(var(--progress, 0) * 3.6deg), #e5e7eb calc(var(--progress, 0) * 3.6deg), #e5e7eb 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  background: white;
  border-radius: 50%;
}

.progress-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #667eea;
  position: relative;
  z-index: 1;
}

.status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.status.completed {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status.incomplete {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

/* 卡片内容 */
.card-content {
  min-height: 80rpx;
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 100rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 列表项 */
.list-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-left: 4rpx solid #667eea;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.item-time {
  font-size: 24rpx;
  color: #666;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
}

/* 技能标签 */
.skills-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.skill-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}

/* 编辑指示器 */
.edit-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  opacity: 0.6;
}

.edit-icon {
  font-size: 24rpx;
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.edit-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.edit-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .save-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f3f4f6;
  color: #666;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
