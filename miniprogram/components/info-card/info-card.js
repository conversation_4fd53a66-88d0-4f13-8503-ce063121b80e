// components/info-card/info-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片类型
    type: {
      type: String,
      value: 'personal' // personal, job, education, experience, skills
    },
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    // 卡片图标
    icon: {
      type: String,
      value: ''
    },
    // 数据
    data: {
      type: Object,
      value: {}
    },
    // 是否已完成
    completed: {
      type: Boolean,
      value: false
    },
    // 完成度百分比
    progress: {
      type: Number,
      value: 0
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isEditing: false,
    formData: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击卡片
     */
    onCardTap() {
      if (!this.data.editable) return
      
      this.setData({
        isEditing: true,
        formData: { ...this.data.data }
      })
    },

    /**
     * 取消编辑
     */
    onCancel() {
      this.setData({
        isEditing: false,
        formData: {}
      })
    },

    /**
     * 保存数据
     */
    onSave() {
      // 验证数据
      if (!this.validateData()) {
        return
      }

      // 触发保存事件
      this.triggerEvent('save', {
        type: this.data.type,
        data: this.data.formData
      })

      this.setData({
        isEditing: false
      })

      // 显示保存成功提示
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },

    /**
     * 输入框变化
     */
    onInputChange(e) {
      const { field } = e.currentTarget.dataset
      const { value } = e.detail
      
      this.setData({
        [`formData.${field}`]: value
      })
    },

    /**
     * 选择器变化
     */
    onPickerChange(e) {
      const { field, options } = e.currentTarget.dataset
      const { value } = e.detail
      
      this.setData({
        [`formData.${field}`]: options[value]
      })
    },

    /**
     * 验证数据
     */
    validateData() {
      const { type, formData } = this.data
      
      switch (type) {
        case 'personal':
          if (!formData.name || !formData.phone) {
            wx.showToast({
              title: '请填写姓名和手机号',
              icon: 'none'
            })
            return false
          }
          break
        case 'job':
          if (!formData.position) {
            wx.showToast({
              title: '请填写期望职位',
              icon: 'none'
            })
            return false
          }
          break
      }
      
      return true
    },

    /**
     * 添加项目（用于教育背景、工作经验等）
     */
    onAddItem() {
      this.triggerEvent('addItem', {
        type: this.data.type
      })
    },

    /**
     * 删除项目
     */
    onDeleteItem(e) {
      const { index } = e.currentTarget.dataset
      this.triggerEvent('deleteItem', {
        type: this.data.type,
        index: index
      })
    }
  }
})
