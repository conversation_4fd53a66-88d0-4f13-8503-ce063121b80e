<!--components/info-card/info-card.wxml-->
<view class="info-card {{completed ? 'completed' : ''}}" bindtap="onCardTap">
  <!-- 卡片头部 -->
  <view class="card-header">
    <view class="header-left">
      <text class="icon">{{icon}}</text>
      <text class="title">{{title}}</text>
    </view>
    <view class="header-right">
      <view class="progress-circle" wx:if="{{progress > 0}}">
        <text class="progress-text">{{progress}}%</text>
      </view>
      <text class="status {{completed ? 'completed' : 'incomplete'}}">
        {{completed ? '已完成' : '待完善'}}
      </text>
    </view>
  </view>

  <!-- 卡片内容 -->
  <view class="card-content">
    <!-- 基本信息卡片 -->
    <block wx:if="{{type === 'personal'}}">
      <view class="info-item" wx:if="{{data.name}}">
        <text class="label">姓名：</text>
        <text class="value">{{data.name}}</text>
      </view>
      <view class="info-item" wx:if="{{data.phone}}">
        <text class="label">手机：</text>
        <text class="value">{{data.phone}}</text>
      </view>
      <view class="info-item" wx:if="{{data.email}}">
        <text class="label">邮箱：</text>
        <text class="value">{{data.email}}</text>
      </view>
      <view class="info-item" wx:if="{{data.location}}">
        <text class="label">地址：</text>
        <text class="value">{{data.location}}</text>
      </view>
      <view class="empty-state" wx:if="{{!data.name && !data.phone}}">
        <text class="empty-text">点击添加个人信息</text>
      </view>
    </block>

    <!-- 求职意向卡片 -->
    <block wx:if="{{type === 'job'}}">
      <view class="info-item" wx:if="{{data.position}}">
        <text class="label">职位：</text>
        <text class="value">{{data.position}}</text>
      </view>
      <view class="info-item" wx:if="{{data.industry}}">
        <text class="label">行业：</text>
        <text class="value">{{data.industry}}</text>
      </view>
      <view class="info-item" wx:if="{{data.location}}">
        <text class="label">地点：</text>
        <text class="value">{{data.location}}</text>
      </view>
      <view class="info-item" wx:if="{{data.salaryRange}}">
        <text class="label">薪资：</text>
        <text class="value">{{data.salaryRange}}</text>
      </view>
      <view class="empty-state" wx:if="{{!data.position}}">
        <text class="empty-text">点击添加求职意向</text>
      </view>
    </block>

    <!-- 教育背景卡片 -->
    <block wx:if="{{type === 'education'}}">
      <view class="list-item" wx:for="{{data}}" wx:key="index">
        <view class="item-header">
          <text class="item-title">{{item.school}}</text>
          <text class="item-time">{{item.startYear}}-{{item.endYear}}</text>
        </view>
        <view class="item-content">
          <text class="item-desc">{{item.major}} · {{item.degree}}</text>
        </view>
      </view>
      <view class="empty-state" wx:if="{{data.length === 0}}">
        <text class="empty-text">点击添加教育背景</text>
      </view>
    </block>

    <!-- 工作经验卡片 -->
    <block wx:if="{{type === 'experience'}}">
      <view class="list-item" wx:for="{{data}}" wx:key="index">
        <view class="item-header">
          <text class="item-title">{{item.company}}</text>
          <text class="item-time">{{item.startDate}}-{{item.endDate}}</text>
        </view>
        <view class="item-content">
          <text class="item-desc">{{item.position}}</text>
        </view>
      </view>
      <view class="empty-state" wx:if="{{data.length === 0}}">
        <text class="empty-text">点击添加工作经验</text>
      </view>
    </block>

    <!-- 技能特长卡片 -->
    <block wx:if="{{type === 'skills'}}">
      <view class="skills-section" wx:if="{{data.technical && data.technical.length > 0}}">
        <text class="section-title">技术技能</text>
        <view class="skills-tags">
          <text class="skill-tag" wx:for="{{data.technical}}" wx:key="index">{{item}}</text>
        </view>
      </view>
      <view class="skills-section" wx:if="{{data.soft && data.soft.length > 0}}">
        <text class="section-title">软技能</text>
        <view class="skills-tags">
          <text class="skill-tag" wx:for="{{data.soft}}" wx:key="index">{{item}}</text>
        </view>
      </view>
      <view class="empty-state" wx:if="{{(!data.technical || data.technical.length === 0) && (!data.soft || data.soft.length === 0)}}">
        <text class="empty-text">点击添加技能特长</text>
      </view>
    </block>
  </view>

  <!-- 编辑按钮 -->
  <view class="edit-indicator" wx:if="{{editable}}">
    <text class="edit-icon">✏️</text>
  </view>
</view>

<!-- 编辑弹窗 -->
<view class="edit-modal {{isEditing ? 'show' : ''}}" wx:if="{{isEditing}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">编辑{{title}}</text>
      <text class="close-btn" bindtap="onCancel">✕</text>
    </view>
    
    <scroll-view class="modal-body" scroll-y>
      <!-- 基本信息编辑表单 -->
      <block wx:if="{{type === 'personal'}}">
        <view class="form-group">
          <text class="form-label">姓名 *</text>
          <input class="form-input" 
                 placeholder="请输入姓名" 
                 value="{{formData.name}}"
                 data-field="name"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">手机号 *</text>
          <input class="form-input" 
                 placeholder="请输入手机号" 
                 value="{{formData.phone}}"
                 data-field="phone"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">邮箱</text>
          <input class="form-input" 
                 placeholder="请输入邮箱" 
                 value="{{formData.email}}"
                 data-field="email"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">所在地</text>
          <input class="form-input" 
                 placeholder="请输入所在地" 
                 value="{{formData.location}}"
                 data-field="location"
                 bindinput="onInputChange" />
        </view>
      </block>

      <!-- 求职意向编辑表单 -->
      <block wx:if="{{type === 'job'}}">
        <view class="form-group">
          <text class="form-label">期望职位 *</text>
          <input class="form-input" 
                 placeholder="请输入期望职位" 
                 value="{{formData.position}}"
                 data-field="position"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">期望行业</text>
          <input class="form-input" 
                 placeholder="请输入期望行业" 
                 value="{{formData.industry}}"
                 data-field="industry"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">工作地点</text>
          <input class="form-input" 
                 placeholder="请输入期望工作地点" 
                 value="{{formData.location}}"
                 data-field="location"
                 bindinput="onInputChange" />
        </view>
        <view class="form-group">
          <text class="form-label">期望薪资</text>
          <input class="form-input" 
                 placeholder="请输入期望薪资范围" 
                 value="{{formData.salaryRange}}"
                 data-field="salaryRange"
                 bindinput="onInputChange" />
        </view>
      </block>
    </scroll-view>

    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCancel">取消</button>
      <button class="save-btn" bindtap="onSave">保存</button>
    </view>
  </view>
</view>
