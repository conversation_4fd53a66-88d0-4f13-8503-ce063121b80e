/* pages/about/about.wxss */

/**
 * 关于页面设计 - 基于原设计系统
 * 极简主义、以内容为核心、故事化体验
 */

.about-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 0;
}

/* 头部区域 */
.header-section {
  padding: 96rpx 32rpx 64rpx;
  text-align: center;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.brand-area {
  max-width: 600rpx;
  margin: 0 auto;
}

.brand-logo {
  margin-bottom: 48rpx;
}

.brand-name {
  display: block;
  font-size: 72rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  letter-spacing: -0.02em;
}

.brand-tagline {
  font-size: 28rpx;
  color: #737373;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.brand-message {
  margin-top: 64rpx;
}

.hero-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #0a0a0a;
  line-height: 1.25;
  margin-bottom: 32rpx;
}

.hero-subtitle {
  display: block;
  font-size: 32rpx;
  color: #737373;
  line-height: 1.75;
  max-width: 480rpx;
  margin: 0 auto;
}

/* 通用区域样式 */
.value-section,
.features-section,
.stats-section {
  padding: 64rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  text-align: center;
  margin-bottom: 64rpx;
  max-width: 600rpx;
  margin-left: auto;
  margin-right: auto;
}

.title-text {
  font-size: 40rpx;
  font-weight: 600;
  color: #0a0a0a;
  line-height: 1.25;
}

/* 价值主张区域 */
.value-list {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.value-item {
  padding: 48rpx 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 150ms ease-out;
}

.value-item:last-child {
  border-bottom: none;
}

.value-item:active {
  background-color: #fafafa;
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 12rpx;
}

.value-content {
  text-align: center;
}

.value-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  line-height: 1.25;
}

.value-description {
  display: block;
  font-size: 28rpx;
  color: #737373;
  line-height: 1.75;
}

/* 产品特色区域 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding: 32rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 150ms ease-out;
}

.feature-item:active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
  transform: translateY(1rpx);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0a0a0a;
  border-radius: 50%;
  flex-shrink: 0;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #0a0a0a;
  line-height: 1.25;
}

.feature-desc {
  font-size: 24rpx;
  color: #737373;
  line-height: 1.5;
}

/* 统计数据区域 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 48rpx 24rpx;
  background-color: #0a0a0a;
  border-radius: 16rpx;
  color: #ffffff;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
  font-weight: 400;
}

/* 操作区域 */
.action-section {
  padding: 64rpx 32rpx;
  background-color: #fafafa;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  max-width: 400rpx;
  margin: 0 auto;
}

/* 底部区域 */
.footer-section {
  padding: 64rpx 32rpx 48rpx;
  background-color: #0a0a0a;
  color: #ffffff;
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 48rpx;
  margin-bottom: 32rpx;
}

.footer-link {
  font-size: 28rpx;
  color: #a3a3a3;
  padding: 16rpx;
  transition: all 150ms ease-out;
}

.footer-link:active {
  color: #ffffff;
}

.footer-info {
  padding-top: 32rpx;
  border-top: 1px solid #737373;
}

.footer-text {
  font-size: 24rpx;
  color: #737373;
  line-height: 1.5;
}

/* 响应式设计 */
@media (min-width: 600rpx) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
  }

  .footer-links {
    gap: 64rpx;
  }
}