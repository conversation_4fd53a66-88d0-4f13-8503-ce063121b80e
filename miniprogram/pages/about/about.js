// pages/about/about.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 产品介绍
    features: [
      {
        icon: 'message-circle',
        title: '对话式制作',
        desc: '像聊天一样轻松制作简历'
      },
      {
        icon: 'settings',
        title: 'AI智能优化',
        desc: '专业AI帮你发现亮点'
      },
      {
        icon: 'briefcase',
        title: '职位匹配',
        desc: '针对性优化提升成功率'
      },
      {
        icon: 'file-text',
        title: '多种模板',
        desc: '精美模板一键生成'
      }
    ],

    // 统计数据
    stats: {
      users: '10,000+',
      resumes: '50,000+',
      success: '95%'
    },

    // 核心价值主张
    valueProps: [
      {
        title: '对话式体验',
        description: '像和朋友聊天一样，自然地分享你的经历'
      },
      {
        title: '智能信息提取',
        description: 'AI自动识别关键信息，构建完整的职业画像'
      },
      {
        title: '专业简历生成',
        description: '基于你的故事，生成符合行业标准的简历'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 开始使用
   */
  onStartUsing() {
    wx.navigateTo({
      url: '/pages/index/index'
    })
  },

  /**
   * 查看示例
   */
  onViewSample() {
    wx.navigateTo({
      url: '/pages/preview/preview?sample=true'
    })
  },

  /**
   * 联系我们
   */
  onContact() {
    wx.showModal({
      title: '联系我们',
      content: '如有问题或建议，请通过小程序反馈功能联系我们',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历故事 - AI帮你写出精彩简历',
      path: '/pages/about/about'
    }
  }
})