// pages/profile/profile.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userProfile: null,
    loading: true,
    overallProgress: 0,

    // 卡片配置
    cardConfigs: [
      {
        type: 'personal',
        title: '基本信息',
        icon: '👤',
        required: true
      },
      {
        type: 'job',
        title: '求职意向',
        icon: '🎯',
        required: true
      },
      {
        type: 'education',
        title: '教育背景',
        icon: '🎓',
        required: false
      },
      {
        type: 'experience',
        title: '工作经验',
        icon: '💼',
        required: false
      },
      {
        type: 'skills',
        title: '技能特长',
        icon: '⚡',
        required: false
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserProfile()
  },

  /**
   * 加载用户信息
   */
  async loadUserProfile() {
    try {
      wx.showLoading({
        title: '加载中...'
      })

      const result = await wx.cloud.callFunction({
        name: 'userProfile',
        data: {
          action: 'get'
        }
      })

      if (result.result.success) {
        const userProfile = result.result.data
        const overallProgress = this.calculateOverallProgress(userProfile)

        this.setData({
          userProfile: userProfile,
          overallProgress: overallProgress,
          loading: false
        })
      } else {
        throw new Error(result.result.error || '加载失败')
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 保存卡片数据
   */
  async onCardSave(e) {
    const { type, data } = e.detail

    try {
      wx.showLoading({
        title: '保存中...'
      })

      // 更新本地数据
      const updatePath = this.getUpdatePath(type)
      const updatedProfile = { ...this.data.userProfile }
      this.setNestedProperty(updatedProfile, updatePath, data)

      // 重新计算完成度
      const completeness = this.calculateCompleteness(updatedProfile)
      updatedProfile.completeness = completeness

      // 保存到云数据库
      const result = await wx.cloud.callFunction({
        name: 'userProfile',
        data: {
          action: 'update',
          data: updatedProfile
        }
      })

      if (result.result.success) {
        // 更新页面数据
        const overallProgress = this.calculateOverallProgress(updatedProfile)
        this.setData({
          userProfile: updatedProfile,
          overallProgress: overallProgress
        })

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } else {
        throw new Error(result.result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    if (this.data.userProfile) {
      this.loadUserProfile()
    }
  },

  /**
   * 获取更新路径
   */
  getUpdatePath(type) {
    const pathMap = {
      'personal': 'personalInfo',
      'job': 'jobIntention',
      'education': 'education',
      'experience': 'workExperience',
      'skills': 'skills'
    }
    return pathMap[type]
  },

  /**
   * 设置嵌套属性
   */
  setNestedProperty(obj, path, value) {
    const keys = path.split('.')
    let current = obj

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }

    current[keys[keys.length - 1]] = value
  },

  /**
   * 计算完成度
   */
  calculateCompleteness(profile) {
    const completeness = {
      personalInfo: 0,
      jobIntention: 0,
      education: 0,
      workExperience: 0,
      projects: 0,
      skills: 0,
      selfEvaluation: 0,
      overall: 0
    }

    // 基本信息完成度
    if (profile.personalInfo) {
      const fields = ['name', 'phone', 'email', 'location']
      const filledFields = fields.filter(field => profile.personalInfo[field])
      completeness.personalInfo = Math.round((filledFields.length / fields.length) * 100)
    }

    // 求职意向完成度
    if (profile.jobIntention) {
      const fields = ['position', 'industry', 'location', 'salaryRange']
      const filledFields = fields.filter(field => profile.jobIntention[field])
      completeness.jobIntention = Math.round((filledFields.length / fields.length) * 100)
    }

    // 教育背景完成度
    completeness.education = profile.education && profile.education.length > 0 ? 100 : 0

    // 工作经验完成度
    completeness.workExperience = profile.workExperience && profile.workExperience.length > 0 ? 100 : 0

    // 技能完成度
    if (profile.skills) {
      const hasSkills = (profile.skills.technical && profile.skills.technical.length > 0) ||
                       (profile.skills.soft && profile.skills.soft.length > 0)
      completeness.skills = hasSkills ? 100 : 0
    }

    // 自我评价完成度
    completeness.selfEvaluation = profile.selfEvaluation ? 100 : 0

    // 总体完成度
    const weights = {
      personalInfo: 0.3,
      jobIntention: 0.3,
      education: 0.15,
      workExperience: 0.15,
      skills: 0.1
    }

    completeness.overall = Math.round(
      completeness.personalInfo * weights.personalInfo +
      completeness.jobIntention * weights.jobIntention +
      completeness.education * weights.education +
      completeness.workExperience * weights.workExperience +
      completeness.skills * weights.skills
    )

    return completeness
  },

  /**
   * 计算总体进度
   */
  calculateOverallProgress(profile) {
    if (!profile || !profile.completeness) return 0
    return profile.completeness.overall || 0
  },

  /**
   * 获取卡片数据
   */
  getCardData(type) {
    if (!this.data.userProfile) return {}

    const dataMap = {
      'personal': this.data.userProfile.personalInfo || {},
      'job': this.data.userProfile.jobIntention || {},
      'education': this.data.userProfile.education || [],
      'experience': this.data.userProfile.workExperience || [],
      'skills': this.data.userProfile.skills || {}
    }

    return dataMap[type] || {}
  },

  /**
   * 获取卡片完成度
   */
  getCardProgress(type) {
    if (!this.data.userProfile || !this.data.userProfile.completeness) return 0

    const progressMap = {
      'personal': this.data.userProfile.completeness.personalInfo || 0,
      'job': this.data.userProfile.completeness.jobIntention || 0,
      'education': this.data.userProfile.completeness.education || 0,
      'experience': this.data.userProfile.completeness.workExperience || 0,
      'skills': this.data.userProfile.completeness.skills || 0
    }

    return progressMap[type] || 0
  },

  /**
   * 生成简历
   */
  onGenerateResume() {
    if (this.data.overallProgress < 50) {
      wx.showModal({
        title: '提示',
        content: '建议完善更多信息后再生成简历，这样效果会更好哦！',
        confirmText: '继续完善',
        cancelText: '直接生成',
        success: (res) => {
          if (!res.confirm) {
            this.navigateToResume()
          }
        }
      })
    } else {
      this.navigateToResume()
    }
  },

  /**
   * 跳转到简历页面
   */
  navigateToResume() {
    wx.navigateTo({
      url: '/pages/resume/resume'
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})