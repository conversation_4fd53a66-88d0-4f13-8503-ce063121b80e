<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 头部进度区域 -->
  <view class="header-section">
    <view class="progress-container">
      <view class="progress-circle-large">
        <view class="progress-fill" style="--progress: {{overallProgress}}%"></view>
        <view class="progress-content">
          <text class="progress-number">{{overallProgress}}%</text>
          <text class="progress-label">完成度</text>
        </view>
      </view>
      <view class="progress-info">
        <text class="welcome-text">你好！</text>
        <text class="subtitle">让我们一起完善你的简历</text>
        <view class="action-buttons">
          <button class="generate-btn" bindtap="onGenerateResume">
            <text class="btn-icon">📄</text>
            <text class="btn-text">生成简历</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 信息卡片列表 -->
  <scroll-view class="cards-container" scroll-y="true">
    <view class="cards-list">
      <info-card
        wx:for="{{cardConfigs}}"
        wx:key="type"
        type="{{item.type}}"
        title="{{item.title}}"
        icon="{{item.icon}}"
        data="{{getCardData(item.type)}}"
        progress="{{getCardProgress(item.type)}}"
        completed="{{getCardProgress(item.type) >= 100}}"
        editable="{{true}}"
        bind:save="onCardSave"
      />
    </view>
  </scroll-view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>