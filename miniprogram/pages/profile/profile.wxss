/* pages/profile/profile.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 头部区域 */
.header-section {
  padding: 60rpx 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

/* 大进度圆环 */
.progress-circle-large {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  border-radius: 50%;
  background: conic-gradient(
    #ffffff 0deg,
    #ffffff calc(var(--progress, 0) * 3.6deg),
    rgba(255, 255, 255, 0.3) calc(var(--progress, 0) * 3.6deg),
    rgba(255, 255, 255, 0.3) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle-large::before {
  content: '';
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
}

.progress-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.progress-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.progress-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-top: 5rpx;
}

/* 进度信息 */
.progress-info {
  flex: 1;
}

.welcome-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.generate-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  backdrop-filter: blur(10rpx);
  margin: 0;
}

.generate-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 卡片容器 */
.cards-container {
  flex: 1;
  max-height: calc(100vh - 300rpx);
}

.cards-list {
  padding: 0 10rpx;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  font-size: 28rpx;
  margin-top: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}