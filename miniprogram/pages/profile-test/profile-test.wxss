/* pages/profile-test/profile-test.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  margin: 0;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.danger {
  background: rgba(255, 59, 48, 0.8);
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
}

/* 测试结果容器 */
.results-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.results-count {
  font-size: 24rpx;
  color: #666;
}

/* 测试结果列表 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #ddd;
}

.result-item.success {
  border-left-color: #22c55e;
  background: rgba(34, 197, 94, 0.05);
}

.result-item.error {
  border-left-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-icon {
  font-size: 24rpx;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.result-time {
  font-size: 20rpx;
  color: #999;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.result-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.result-message {
  font-size: 24rpx;
  color: #22c55e;
}

.result-error {
  font-size: 24rpx;
  color: #ef4444;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 信息说明 */
.info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.info-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.info-desc {
  font-size: 24rpx;
  color: #666;
}
