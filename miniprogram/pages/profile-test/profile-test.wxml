<!--pages/profile-test/profile-test.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">🧪 用户信息系统测试</text>
    <text class="subtitle">测试卡片系统和数据库功能</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests" disabled="{{isLoading}}">
      {{isLoading ? '测试中...' : '重新测试'}}
    </button>
    <button class="action-btn secondary" bindtap="goToProfile">
      进入正式页面
    </button>
    <button class="action-btn danger" bindtap="clearTestData">
      清除测试数据
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="results-container">
    <view class="results-header">
      <text class="results-title">测试结果</text>
      <text class="results-count">{{testResults.length}} 项测试</text>
    </view>

    <view class="results-list">
      <view class="result-item {{item.status}}" wx:for="{{testResults}}" wx:key="name">
        <view class="result-header">
          <view class="result-status">
            <text class="status-icon">{{item.status === 'success' ? '✅' : '❌'}}</text>
            <text class="status-text">{{item.status === 'success' ? '成功' : '失败'}}</text>
          </view>
          <text class="result-time">{{item.time}}</text>
        </view>
        
        <view class="result-content">
          <text class="result-name">{{item.name}}</text>
          <text class="result-message" wx:if="{{item.result}}">{{item.result}}</text>
          <text class="result-error" wx:if="{{item.error}}">错误: {{item.error}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{testResults.length === 0 && !isLoading}}">
      <text class="empty-text">点击"重新测试"开始测试</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在运行测试...</text>
    </view>
  </view>

  <!-- 测试说明 -->
  <view class="info-section">
    <text class="info-title">📋 测试项目说明</text>
    <view class="info-list">
      <view class="info-item">
        <text class="info-label">云函数连接</text>
        <text class="info-desc">测试userProfile云函数是否正常部署</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户信息创建</text>
        <text class="info-desc">测试创建新用户档案功能</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户信息获取</text>
        <text class="info-desc">测试从数据库读取用户信息</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户信息更新</text>
        <text class="info-desc">测试更新用户信息到数据库</text>
      </view>
      <view class="info-item">
        <text class="info-label">完成度计算</text>
        <text class="info-desc">测试信息完成度自动计算功能</text>
      </view>
    </view>
  </view>
</view>
