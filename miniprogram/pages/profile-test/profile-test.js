// pages/profile-test/profile-test.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    testResults: [],
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.runTests()
  },

  /**
   * 运行测试
   */
  async runTests() {
    this.setData({ isLoading: true })
    
    const tests = [
      { name: '测试云函数连接', func: this.testCloudFunction },
      { name: '测试用户信息创建', func: this.testCreateProfile },
      { name: '测试用户信息获取', func: this.testGetProfile },
      { name: '测试用户信息更新', func: this.testUpdateProfile },
      { name: '测试完成度计算', func: this.testCompletenessCalculation }
    ]

    const results = []
    
    for (const test of tests) {
      try {
        const result = await test.func.call(this)
        results.push({
          name: test.name,
          status: 'success',
          result: result,
          time: new Date().toLocaleTimeString()
        })
      } catch (error) {
        results.push({
          name: test.name,
          status: 'error',
          error: error.message,
          time: new Date().toLocaleTimeString()
        })
      }
    }

    this.setData({
      testResults: results,
      isLoading: false
    })
  },

  /**
   * 测试云函数连接
   */
  async testCloudFunction() {
    const result = await wx.cloud.callFunction({
      name: 'userProfile',
      data: {
        action: 'get'
      }
    })
    
    if (result.result.success) {
      return '云函数连接正常'
    } else {
      throw new Error('云函数连接失败')
    }
  },

  /**
   * 测试用户信息创建
   */
  async testCreateProfile() {
    const testData = {
      personalInfo: {
        name: '测试用户',
        phone: '13800138000',
        email: '<EMAIL>',
        location: '北京市'
      },
      jobIntention: {
        position: '前端开发工程师',
        industry: '互联网',
        location: '北京',
        salaryRange: '15-25K'
      }
    }

    const result = await wx.cloud.callFunction({
      name: 'userProfile',
      data: {
        action: 'update',
        data: testData
      }
    })

    if (result.result.success) {
      return '用户信息创建成功'
    } else {
      throw new Error('用户信息创建失败')
    }
  },

  /**
   * 测试用户信息获取
   */
  async testGetProfile() {
    const result = await wx.cloud.callFunction({
      name: 'userProfile',
      data: {
        action: 'get'
      }
    })

    if (result.result.success && result.result.data) {
      return `获取到用户信息，ID: ${result.result.data._id}`
    } else {
      throw new Error('用户信息获取失败')
    }
  },

  /**
   * 测试用户信息更新
   */
  async testUpdateProfile() {
    const updateData = {
      personalInfo: {
        name: '更新测试用户',
        phone: '13900139000',
        email: '<EMAIL>',
        location: '上海市'
      }
    }

    const result = await wx.cloud.callFunction({
      name: 'userProfile',
      data: {
        action: 'update',
        data: updateData
      }
    })

    if (result.result.success) {
      return '用户信息更新成功'
    } else {
      throw new Error('用户信息更新失败')
    }
  },

  /**
   * 测试完成度计算
   */
  async testCompletenessCalculation() {
    // 获取当前用户信息
    const result = await wx.cloud.callFunction({
      name: 'userProfile',
      data: {
        action: 'get'
      }
    })

    if (result.result.success && result.result.data.completeness) {
      const completeness = result.result.data.completeness
      return `完成度计算正常，总体完成度: ${completeness.overall}%`
    } else {
      throw new Error('完成度计算失败')
    }
  },

  /**
   * 清除测试数据
   */
  async clearTestData() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除测试数据吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await wx.cloud.callFunction({
              name: 'userProfile',
              data: {
                action: 'delete'
              }
            })
            
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 重新运行测试
   */
  rerunTests() {
    this.setData({ testResults: [] })
    this.runTests()
  },

  /**
   * 跳转到正式页面
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  }
})
