// pages/index/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    hasUserInfo: false,

    // 快速开始选项
    quickStartOptions: [
      {
        id: 'fresh_graduate',
        title: '我是应届毕业生',
        desc: '刚毕业，想要制作第一份简历',
        icon: 'graduation-cap'
      },
      {
        id: 'job_hopping',
        title: '我想要跳槽',
        desc: '有工作经验，寻找更好机会',
        icon: 'trending-up'
      },
      {
        id: 'career_change',
        title: '我正在转行',
        desc: '转换职业方向，重新开始',
        icon: 'refresh-cw'
      }
    ],

    // 最近对话
    recentConversations: [],

    // 简历完成度
    resumeProgress: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.checkUserInfo()
    this.loadRecentData()
  },

  /**
   * 检查用户信息
   */
  checkUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
  },

  /**
   * 加载最近数据
   */
  async loadRecentData() {
    try {
      // 加载最近对话
      const conversations = wx.getStorageSync('recentConversations') || []

      // 加载简历进度
      const app = getApp()
      let progress = 0
      if (app.globalData.resumeData) {
        progress = app.globalData.resumeData.completeness || 0
      }

      this.setData({
        recentConversations: conversations.slice(0, 3), // 只显示最近3个
        resumeProgress: progress
      })
    } catch (error) {
      console.error('加载最近数据失败:', error)
    }
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善简历信息',
      success: (res) => {
        const userInfo = res.userInfo
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        })

        // 保存用户信息
        wx.setStorageSync('userInfo', userInfo)

        // 跳转到对话页面
        this.startResumeChat()
      },
      fail: () => {
        wx.showToast({
          title: '需要授权才能使用',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 跳转到个人信息页面
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  },

  /**
   * 开始简历对话
   */
  startResumeChat() {
    wx.navigateTo({
      url: '/pages/chat/chat'
    })
  },

  /**
   * 查看示例简历
   */
  viewSampleResume() {
    wx.navigateTo({
      url: '/pages/preview/preview?sample=true'
    })
  },

  /**
   * 快速开始 - 根据选项直接进入对话
   */
  onQuickStart(e) {
    const { option } = e.currentTarget.dataset

    if (!this.data.hasUserInfo) {
      this.getUserProfile()
      return
    }

    // 带着选项信息跳转到对话页面
    wx.navigateTo({
      url: `/pages/chat/chat?quickStart=${option.id}&title=${encodeURIComponent(option.title)}`
    })
  },

  /**
   * 开始新对话
   */
  startNewChat() {
    if (!this.data.hasUserInfo) {
      this.getUserProfile()
      return
    }

    wx.navigateTo({
      url: '/pages/chat/chat'
    })
  },

  /**
   * 继续最近对话
   */
  continueConversation(e) {
    const { conversationId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/chat/chat?conversationId=${conversationId}`
    })
  },

  /**
   * 查看简历预览
   */
  viewResume() {
    wx.navigateTo({
      url: '/pages/preview/preview'
    })
  },

  /**
   * 查看关于页面
   */
  viewAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  /**
   * 查看帮助
   */
  viewHelp() {
    wx.showModal({
      title: '如何使用',
      content: '1. 点击"开始制作"按钮\n2. 与AI助手对话，分享你的经历\n3. AI会自动整理信息\n4. 选择模板生成精美简历\n5. 导出或分享你的简历',
      showCancel: false,
      confirmText: '我知道了'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历故事 - AI帮你写出精彩简历',
      path: '/pages/index/index',
      imageUrl: '/imgs/share-cover.png'
    }
  }
})
