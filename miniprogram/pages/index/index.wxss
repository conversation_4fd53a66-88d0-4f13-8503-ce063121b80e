/* pages/index/index.wxss */

/**
 * 首页设计 - 基于原设计系统
 * 极简主义、以内容为核心、故事化体验
 */

/* ========================================
   页面布局
   ======================================== */

.home-page {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* ========================================
   头部区域
   ======================================== */

.header-section {
  padding: 64rpx 32rpx 48rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.brand-minimal {
  text-align: center;
  margin-bottom: 48rpx;
}

.app-name {
  display: block;
  font-size: 60rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  letter-spacing: -0.02em;
}

.app-subtitle {
  font-size: 28rpx;
  color: #737373;
  font-weight: 400;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 150ms ease-out;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2px solid #e5e5e5;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #0a0a0a;
}

.progress-text {
  font-size: 24rpx;
  color: #737373;
}

/* ========================================
   主要功能区域
   ======================================== */

.main-section {
  flex: 1;
  padding: 32rpx;
  max-width: 600rpx;
  margin: 0 auto;
  width: 100%;
}

/* 快速开始区域 */
.quick-start-section {
  margin-bottom: 64rpx;
}

.section-title {
  text-align: center;
  margin-bottom: 48rpx;
}

.title-text {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  line-height: 1.25;
}

.subtitle-text {
  font-size: 28rpx;
  color: #737373;
  line-height: 1.5;
}

.quick-options {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.quick-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 150ms ease-out;
}

.quick-option:active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
  transform: translateY(1rpx);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0a0a0a;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.option-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #0a0a0a;
  line-height: 1.25;
}

.option-desc {
  font-size: 24rpx;
  color: #737373;
  line-height: 1.5;
}

.option-arrow,
.recent-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.arrow-text {
  font-size: 24rpx;
  color: #a3a3a3;
}

/* 已登录用户功能区域 */
.main-functions {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.new-chat-section {
  text-align: center;
  padding: 64rpx 32rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  transition: all 150ms ease-out;
}

.new-chat-btn {
  margin-bottom: 24rpx;
}

.new-chat-desc {
  font-size: 28rpx;
  color: #737373;
  line-height: 1.5;
}

/* 最近对话区域 */
.recent-section {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  padding: 32rpx;
}

.section-header {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #0a0a0a;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 150ms ease-out;
}

.recent-item:active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
}

.recent-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.recent-title {
  font-size: 28rpx;
  color: #0a0a0a;
  font-weight: 500;
  line-height: 1.25;
}

.recent-time {
  font-size: 24rpx;
  color: #737373;
}

/* 快捷功能网格 */
.quick-functions {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  padding: 32rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 24rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 150ms ease-out;
}

.function-item:active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
  transform: translateY(1rpx);
}

.function-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0a0a0a;
  border-radius: 50%;
}

.function-text {
  font-size: 24rpx;
  color: #0a0a0a;
  font-weight: 500;
  text-align: center;
}

/* 授权提示区域 */
.auth-prompt {
  padding: 48rpx 32rpx;
  background-color: #fafafa;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.prompt-text {
  display: block;
  font-size: 28rpx;
  color: #737373;
  margin-bottom: 32rpx;
  line-height: 1.5;
  max-width: 480rpx;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (min-width: 600rpx) {
  .function-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .main-section {
    padding: 48rpx 32rpx;
  }
}


/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .hero-title {
    font-size: 40rpx;
  }

  .brand-name {
    font-size: 60rpx;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .btn {
    width: 100%;
  }

  .footer-info {
    gap: 32rpx;
  }
}