<!--pages/index/index.wxml-->
<view class="home-page">
  <!-- 头部区域 -->
  <view class="header-section">
    <view class="brand-minimal">
      <text class="app-name">简历故事</text>
      <text class="app-subtitle">AI简历助手</text>
    </view>

    <!-- 用户信息区域 -->
    <view wx:if="{{hasUserInfo}}" class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName}}</text>
        <text class="progress-text" wx:if="{{resumeProgress > 0}}">简历完成度 {{resumeProgress}}%</text>
      </view>
    </view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-section">
    <!-- 快速开始选项 -->
    <view wx:if="{{!hasUserInfo}}" class="quick-start-section">
      <view class="section-title">
        <text class="title-text">开始制作你的简历</text>
        <text class="subtitle-text">选择一个选项快速开始</text>
      </view>

      <view class="quick-options">
        <view
          class="quick-option"
          wx:for="{{quickStartOptions}}"
          wx:key="id"
          data-option="{{item}}"
          bindtap="onQuickStart"
        >
          <view class="option-icon">
            <ui-icon name="{{item.icon}}" size="48" color="#ffffff"></ui-icon>
          </view>
          <view class="option-content">
            <text class="option-title">{{item.title}}</text>
            <text class="option-desc">{{item.desc}}</text>
          </view>
          <view class="option-arrow">
            <ui-icon name="chevron-right" size="32" color="#a3a3a3"></ui-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 已登录用户的主要功能 -->
    <view wx:else class="main-functions">
      <!-- 新对话按钮 -->
      <view class="new-chat-section">
        <ui-button
          variant="primary"
          size="large"
          text="开始新对话"
          className="new-chat-btn"
          bindtap="startNewChat"
        ></ui-button>
        <text class="new-chat-desc">与AI助手对话，完善你的简历信息</text>
      </view>

      <!-- 最近对话 -->
      <view wx:if="{{recentConversations.length > 0}}" class="recent-section">
        <view class="section-header">
          <text class="section-title">最近对话</text>
        </view>
        <view class="recent-list">
          <view
            class="recent-item"
            wx:for="{{recentConversations}}"
            wx:key="id"
            data-conversation-id="{{item.id}}"
            bindtap="continueConversation"
          >
            <view class="recent-content">
              <text class="recent-title">{{item.title}}</text>
              <text class="recent-time">{{item.updateTime}}</text>
            </view>
            <view class="recent-arrow">
              <ui-icon name="chevron-right" size="32" color="#a3a3a3"></ui-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷功能 -->
      <view class="quick-functions">
        <view class="function-grid">
          <view class="function-item" bindtap="goToProfile">
            <view class="function-icon">
              <ui-icon name="user" size="32" color="#ffffff"></ui-icon>
            </view>
            <text class="function-text">个人资料</text>
          </view>

          <view class="function-item" bindtap="viewResume">
            <view class="function-icon">
              <ui-icon name="file-text" size="32" color="#ffffff"></ui-icon>
            </view>
            <text class="function-text">简历预览</text>
          </view>

          <view class="function-item" bindtap="viewAbout">
            <view class="function-icon">
              <ui-icon name="info" size="32" color="#ffffff"></ui-icon>
            </view>
            <text class="function-text">关于我们</text>
          </view>

          <view class="function-item" bindtap="viewHelp">
            <view class="function-icon">
              <ui-icon name="help-circle" size="32" color="#ffffff"></ui-icon>
            </view>
            <text class="function-text">使用帮助</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部提示 -->
  <view wx:if="{{!hasUserInfo}}" class="auth-prompt">
    <text class="prompt-text">需要授权微信信息来个性化你的简历体验</text>
    <ui-button
      variant="primary"
      size="default"
      text="授权并开始"
      bindtap="getUserProfile"
    ></ui-button>
  </view>
</view>

